{"name": "tz-mobile", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/uuid": "^10.0.0", "antd-mobile": "^5.39.0", "antd-mobile-icons": "^0.3.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^3", "@next/eslint-plugin-next": "^15.2.4", "@nx/eslint": "^21.1.2", "@nx/eslint-plugin": "^21.1.2", "@nx/jest": "^21.1.2", "@nx/js": "^21.1.2", "@nx/next": "^21.1.2", "@nx/react": "^21.1.2", "@nx/workspace": "^21.1.2", "@swc-node/register": "^1.10.10", "@swc/cli": "~0.6.0", "@swc/core": "^1.11.29", "@swc/helpers": "~0.5.11", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "nx": "21.1.2", "prettier": "^2.6.2", "tailwindcss": "^4", "tslib": "^2.3.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}, "nx": {}}