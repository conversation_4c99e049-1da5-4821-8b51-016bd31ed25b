# 下一步开发指南

## 立即可执行的任务

### 1. 创建剩余核心库 (30分钟)

```bash
# 创建网络请求库
npx nx g @nx/js:lib core-network --directory=libs/core/network --importPath=@tz-mobile/core-network --unitTestRunner=none --bundler=rollup

# 创建认证库
npx nx g @nx/react:lib core-auth --directory=libs/core/auth --importPath=@tz-mobile/core-auth --unitTestRunner=none --bundler=rollup --style=css

# 创建工具库
npx nx g @nx/js:lib utils-helpers --directory=libs/utils/helpers --importPath=@tz-mobile/utils-helpers --unitTestRunner=none --bundler=rollup

# 创建表单功能库
npx nx g @nx/react:lib feature-form --directory=libs/features/form --importPath=@tz-mobile/feature-form --unitTestRunner=none --bundler=rollup --style=css
```

### 2. 迁移现有组件到UI库 (45分钟)

#### 2.1 迁移TabBar组件
```bash
# 将现有的TabBar组件移动到UI库
cp app/components/TabBar.tsx libs/ui/components/src/lib/
```

然后在 `libs/ui/components/src/index.ts` 中导出：
```typescript
export { default as TabBar } from './lib/TabBar';
```

#### 2.2 迁移其他组件
```bash
# 迁移表单相关组件
mkdir -p libs/ui/components/src/lib/form
cp app/components/FormClient.tsx libs/ui/components/src/lib/form/
cp app/components/FieldRenderer.tsx libs/ui/components/src/lib/form/
cp app/components/TextInput.tsx libs/ui/components/src/lib/form/
# ... 其他表单组件
```

### 3. 创建状态管理结构 (30分钟)

在 `libs/core/state/src/lib/` 中创建：

```typescript
// stores/index.ts
export { useGlobalStore } from './global-store';
export { useFormStore } from './form-store';
export { useAuthStore } from './auth-store';

// global-store.ts
import { create } from 'zustand';

interface GlobalState {
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

export const useGlobalStore = create<GlobalState>((set) => ({
  loading: false,
  setLoading: (loading) => set({ loading }),
}));
```

### 4. 配置依赖约束 (15分钟)

在 `nx.json` 中添加：

```json
{
  "namedInputs": {
    "default": ["{projectRoot}/**/*", "sharedGlobals"],
    "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)"]
  },
  "targetDefaults": {
    "build": {
      "dependsOn": ["^build"]
    }
  },
  "generators": {
    "@nx/react": {
      "library": {
        "linter": "eslint"
      }
    }
  }
}
```

## 本周开发计划

### Day 1: 基础库完善
- [ ] 创建所有核心库
- [ ] 迁移现有组件
- [ ] 配置基础的状态管理

### Day 2: 网络层实现
- [ ] 在core-network中实现Axios封装
- [ ] 添加请求拦截器和响应拦截器
- [ ] 实现错误处理机制

### Day 3: 认证系统
- [ ] 实现登录认证逻辑
- [ ] 添加token管理
- [ ] 集成路由守卫

### Day 4: 表单系统
- [ ] 完善表单组件库
- [ ] 实现动态表单功能
- [ ] 添加表单验证

### Day 5: 集成测试
- [ ] 测试所有库的构建
- [ ] 验证依赖关系
- [ ] 性能测试

## 代码迁移检查清单

### 组件迁移
- [ ] TabBar → `@tz-mobile/ui-components`
- [ ] FormClient → `@tz-mobile/feature-form`
- [ ] FieldRenderer → `@tz-mobile/ui-components`
- [ ] LoadingClient → `@tz-mobile/ui-components`
- [ ] 所有Field组件 → `@tz-mobile/ui-components`

### 状态管理迁移
- [ ] globalStore → `@tz-mobile/core-state`
- [ ] stateStore → `@tz-mobile/core-state`
- [ ] 表单状态 → `@tz-mobile/feature-form`

### 类型定义迁移
- [ ] IGlobalStore → `@tz-mobile/shared-models`
- [ ] IStateStore → `@tz-mobile/shared-models`
- [ ] 表单类型 → `@tz-mobile/shared-models`

## 验证步骤

### 1. 构建验证
```bash
# 构建所有库
npx nx run-many -t build

# 构建主应用
npx nx build mobile-app
```

### 2. 依赖验证
```bash
# 查看项目依赖图
npx nx graph

# 检查循环依赖
npx nx graph --focus=mobile-app
```

### 3. 代码质量验证
```bash
# 运行ESLint
npx nx run-many -t lint

# 检查类型
npx nx run-many -t type-check
```

## 常见问题解决

### 1. 导入路径问题
如果遇到导入路径错误，检查：
- `tsconfig.base.json` 中的路径映射
- 库的 `src/index.ts` 导出

### 2. 构建失败
检查：
- 依赖关系是否正确
- ESLint配置是否有冲突
- TypeScript配置是否正确

### 3. 热重载问题
确保：
- Next.js配置正确
- 库的变更能触发重新构建

## 性能优化建议

1. **使用Nx缓存**
   ```bash
   # 启用远程缓存
   npx nx connect-to-nx-cloud
   ```

2. **增量构建**
   ```bash
   # 只构建受影响的项目
   npx nx affected:build
   ```

3. **并行执行**
   ```bash
   # 并行运行多个任务
   npx nx run-many -t build --parallel=3
   ```

## 下周计划预览

1. **插件系统设计**
   - 定义插件接口
   - 实现插件加载机制
   - 创建示例插件

2. **主题系统**
   - 设计主题配置
   - 实现主题切换
   - 集成TailwindCSS

3. **测试系统**
   - 配置Jest
   - 添加组件测试
   - 集成E2E测试

按照这个指南，您可以在一周内完成基础的Nx集成和代码迁移工作。
