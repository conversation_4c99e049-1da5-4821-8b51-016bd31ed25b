{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@tz-mobile/core-auth": ["libs/core/auth/src/index.ts"], "@tz-mobile/core-network": ["libs/core/network/src/index.ts"], "@tz-mobile/core-router": ["libs/core/router/src/index.ts"], "@tz-mobile/core-state": ["libs/core/state/src/index.ts"], "@tz-mobile/feature-auth": ["libs/features/auth/src/index.ts"], "@tz-mobile/feature-form": ["libs/features/form/src/index.ts"], "@tz-mobile/feature-list": ["libs/features/list/src/index.ts"], "@tz-mobile/feature-todo": ["libs/features/todo/src/index.ts"], "@tz-mobile/shared-constants": ["libs/shared/constants/src/index.ts"], "@tz-mobile/shared-interfaces": ["libs/shared/interfaces/src/index.ts"], "@tz-mobile/shared-models": ["libs/shared/models/src/index.ts"], "@tz-mobile/shared-types": ["libs/shared/types/src/index.ts"], "@tz-mobile/ui-components": ["libs/ui/components/src/index.ts"], "@tz-mobile/ui-templates": ["libs/ui/templates/src/index.ts"], "@tz-mobile/ui-theme": ["libs/ui/theme/src/index.ts"], "@tz-mobile/utils-formatters": ["libs/utils/formatters/src/index.ts"], "@tz-mobile/utils-helpers": ["libs/utils/helpers/src/index.ts"], "@tz-mobile/utils-validators": ["libs/utils/validators/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}