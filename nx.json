{"$schema": "./node_modules/nx/schemas/nx-schema.json", "nxCloudId": "683515c4a82e6a4cdf285495", "targetDefaults": {"build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/eslint.config.mjs"]}, "dev": {"cache": false}, "start": {"cache": false}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json"], "sharedGlobals": ["{workspaceRoot}/nx.json", "{workspaceRoot}/package.json", "{workspaceRoot}/tsconfig.base.json"]}, "generators": {"@nx/react": {"application": {"style": "css", "linter": "eslint", "bundler": "webpack"}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint", "unitTestRunner": "none"}}, "@nx/next": {"application": {"style": "css", "linter": "eslint"}}}, "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint", "test", "e2e"]}}}, "plugins": [{"plugin": "@nx/next/plugin", "options": {"startTargetName": "next:start", "buildTargetName": "next:build", "devTargetName": "next:dev", "serveStaticTargetName": "serve-static", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/rollup/plugin", "options": {"buildTargetName": "build", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}]}