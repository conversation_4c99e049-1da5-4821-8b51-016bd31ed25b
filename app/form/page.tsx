// 表单页面组件
import { GET } from '../api/form/layout/route';
import { NextRequest } from 'next/server';
import FormClient from '../components/FormClient';
import { Suspense } from 'react';
import LoadingClient from '../components/LoadingClient';

/**
 * 将布局数据转换为映射对象，以唯一键为键，配置为值
 * @param layout 布局数据
 * @returns 映射对象，键为唯一标识，值为去除children的配置
 */
function createLayoutMapping(layout: any): Record<string, any> {
  const mapping: Record<string, any> = {};
  
  // 递归处理布局元素
  function processLayoutItem(item: any): string {
    if (!item || !item.code) {
      console.warn('布局项缺少code属性', item);
      return '';
    }
    
    // 生成唯一键
    const formCode = item.config?.baseConfig?.formCode || '';
    const key = formCode ? `${formCode}:${item.code}` : item.code;
    
    // 处理子元素，收集子元素的键
    const childrenKeys: string[] = [];
    if (item.children && Array.isArray(item.children)) {
      item.children.forEach((child: any) => {
        // 确保子元素也考虑formCode，这里直接使用子元素自己的baseConfig.formCode
        // 如果子元素没有formCode，则继承父元素的formCode
        
        const childKey = processLayoutItem(child);
        if (childKey) {
          childrenKeys.push(childKey);
        }
      });
    }
    
    // 创建不包含children的配置副本
    const configWithoutChildren = { ...item };
    delete configWithoutChildren.children;
    
    // 添加childrenKeys替代children
    if (childrenKeys.length > 0) {
      configWithoutChildren.childrenKeys = childrenKeys;
    }
    
    // 添加到映射
    mapping[key] = configWithoutChildren;
    
    return key;
  }
  
  // 处理顶层元素
  if (Array.isArray(layout)) {
    layout.forEach(item => processLayoutItem(item));
  } else if (layout) {
    processLayoutItem(layout);
  }
  
  return mapping;
}

// Loading组件
function FormLoading() {
  return <LoadingClient />;
}

// 表单内容组件
async function FormContent() {
  try {
    // 创建模拟的请求对象
    const mockRequest = new NextRequest('http://localhost:3000/api/form/layout');
    
    // 直接调用API路由处理函数获取数据
    const response = await GET(mockRequest);
    const data = await response.json();
    
    // 当获取数据失败时的错误处理
    if (data.code !== 0) {
      return (
        <div className="p-4">
          <h1 className="text-xl font-bold mb-4">表单加载失败</h1>
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <p>{data.message || '获取表单布局失败'}</p>
          </div>
        </div>
      );
    }
    
    // 获取布局数据并传递给客户端组件
    const layoutData = data.data;

    // 将layout转成map，以baseConfig.formCode加code组成唯一键
    const layoutMapping = createLayoutMapping(layoutData.layout.layout);
    
    // 渲染包含客户端组件的页面
    return (
      <div >
        <FormClient layoutData={layoutData} layoutMapping={layoutMapping} />
      </div>
    );
  } catch (error) {
    console.error('表单数据请求错误:', error);
    return (
      <div className="p-4">
        <h1 className="text-xl font-bold mb-4">表单加载失败</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error instanceof Error ? error.message : '未知错误'}</p>
        </div>
      </div>
    );
  }
}

// 服务端页面组件
export default function FormPage() {
  return (
    <Suspense fallback={<FormLoading />}>
      <FormContent />
    </Suspense>
  );
} 