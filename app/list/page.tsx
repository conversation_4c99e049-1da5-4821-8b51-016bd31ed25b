'use client';

import React, { useState } from 'react';
import { Tabs, List, Tag, Button, Dialog, Toast } from 'antd-mobile';
import { SearchOutline, FilterOutline, RightOutline } from 'antd-mobile-icons';

// 模拟订单数据
const mockOrderList = [
  {
    id: 'DD2023100100001',
    title: '五金配件采购订单',
    customer: '上海科技有限公司',
    amount: 12500.00,
    status: '待审核',
    createTime: '2023-10-01 10:30:25',
    payStatus: '未支付',
    priority: '高',
    type: '采购订单'
  },
  {
    id: 'DD2023100200002',
    title: '办公设备批发订单',
    customer: '北京商贸集团',
    amount: 35800.50,
    status: '已审核',
    createTime: '2023-10-02 09:15:10',
    payStatus: '部分支付',
    priority: '中',
    type: '销售订单'
  },
  {
    id: 'DD2023100300003',
    title: '原材料采购订单',
    customer: '广州制造厂',
    amount: 68000.00,
    status: '处理中',
    createTime: '2023-10-03 14:22:45',
    payStatus: '未支付',
    priority: '中',
    type: '采购订单'
  },
  {
    id: 'DD2023100400004',
    title: '电子元件批发订单',
    customer: '深圳电子科技有限公司',
    amount: 29600.75,
    status: '已完成',
    createTime: '2023-10-04 16:40:30',
    payStatus: '已支付',
    priority: '低',
    type: '销售订单'
  },
  {
    id: 'DD2023100500005',
    title: '设备维修服务订单',
    customer: '杭州网络科技公司',
    amount: 4800.00,
    status: '已取消',
    createTime: '2023-10-05 11:05:15',
    payStatus: '已退款',
    priority: '高',
    type: '服务订单'
  },
  {
    id: 'DD2023100600006',
    title: '季度办公用品订单',
    customer: '成都软件园',
    amount: 7320.25,
    status: '待审核',
    createTime: '2023-10-06 08:55:40',
    payStatus: '未支付',
    priority: '中',
    type: '采购订单'
  },
  {
    id: 'DD2023100700007',
    title: '设备安装订单',
    customer: '南京通信公司',
    amount: 15800.50,
    status: '处理中',
    createTime: '2023-10-07 13:28:20',
    payStatus: '部分支付',
    priority: '高',
    type: '服务订单'
  },
  {
    id: 'DD2023100800008',
    title: '年度合作协议订单',
    customer: '武汉工业集团',
    amount: 125000.00,
    status: '已审核',
    createTime: '2023-10-08 15:10:30',
    payStatus: '未支付',
    priority: '高',
    type: '框架订单'
  }
];

// 获取状态对应的样式
const getStatusStyle = (status: string) => {
  switch (status) {
    case '待审核':
      return { color: '#f59e0b', bg: '#fef3c7' };
    case '已审核':
      return { color: '#3b82f6', bg: '#dbeafe' };
    case '处理中':
      return { color: '#8b5cf6', bg: '#ede9fe' };
    case '已完成':
      return { color: '#10b981', bg: '#d1fae5' };
    case '已取消':
      return { color: '#ef4444', bg: '#fee2e2' };
    default:
      return { color: '#6b7280', bg: '#f3f4f6' };
  }
};

// 获取支付状态对应的样式
const getPayStatusStyle = (payStatus: string) => {
  switch (payStatus) {
    case '未支付':
      return { color: '#ef4444', bg: '#fee2e2' };
    case '部分支付':
      return { color: '#f59e0b', bg: '#fef3c7' };
    case '已支付':
      return { color: '#10b981', bg: '#d1fae5' };
    case '已退款':
      return { color: '#3b82f6', bg: '#dbeafe' };
    default:
      return { color: '#6b7280', bg: '#f3f4f6' };
  }
};

// 获取优先级对应的样式
const getPriorityStyle = (priority: string) => {
  switch (priority) {
    case '高':
      return { color: '#ef4444', bg: '#fee2e2' };
    case '中':
      return { color: '#f59e0b', bg: '#fef3c7' };
    case '低':
      return { color: '#10b981', bg: '#d1fae5' };
    default:
      return { color: '#6b7280', bg: '#f3f4f6' };
  }
};

export default function ListPage() {
  const [activeTab, setActiveTab] = useState('all');
  
  // 根据标签筛选订单
  const getFilteredOrders = () => {
    switch (activeTab) {
      case 'pending':
        return mockOrderList.filter(order => order.status === '待审核');
      case 'processing':
        return mockOrderList.filter(order => order.status === '处理中' || order.status === '已审核');
      case 'completed':
        return mockOrderList.filter(order => order.status === '已完成');
      case 'canceled':
        return mockOrderList.filter(order => order.status === '已取消');
      default:
        return mockOrderList;
    }
  };

  // 处理订单点击事件
  const handleOrderClick = (order: any) => {
    Dialog.alert({
      title: '订单详情',
      content: (
        <div className="p-2">
          <div className="py-1">订单编号: <span className="font-medium">{order.id}</span></div>
          <div className="py-1">订单标题: <span className="font-medium">{order.title}</span></div>
          <div className="py-1">客户名称: <span className="font-medium">{order.customer}</span></div>
          <div className="py-1">订单金额: <span className="font-medium text-red-500">￥{order.amount.toFixed(2)}</span></div>
          <div className="py-1">订单状态: <span className="font-medium">{order.status}</span></div>
          <div className="py-1">支付状态: <span className="font-medium">{order.payStatus}</span></div>
          <div className="py-1">创建时间: <span className="font-medium">{order.createTime}</span></div>
          <div className="py-1">优先级: <span className="font-medium">{order.priority}</span></div>
          <div className="py-1">订单类型: <span className="font-medium">{order.type}</span></div>
        </div>
      ),
      closeOnMaskClick: true,
    });
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    Toast.show({
      content: '搜索功能开发中...',
      position: 'bottom',
    });
  };

  // 处理筛选按钮点击
  const handleFilter = () => {
    Toast.show({
      content: '筛选功能开发中...',
      position: 'bottom',
    });
  };

  // 格式化金额
  const formatAmount = (amount: number) => {
    return `￥${amount.toFixed(2)}`;
  };

  return (
    <div className="pb-16 bg-gray-50">
      {/* 页面标题 */}
      <div className="p-3 text-lg text-center bg-white">订单列表</div>
      
      {/* 搜索和筛选区域 */}
      <div className="flex items-center justify-between px-4 py-2 bg-white">
        <div className="flex items-center flex-1 mr-2 bg-gray-100 rounded-sm px-2 py-1">
          <SearchOutline className="text-gray-400 mr-2" />
          <span className="text-gray-400">搜索订单</span>
        </div>
        <div className="flex items-center bg-gray-100 rounded-sm px-2 py-1">
          <span className="text-gray-400 mr-1">筛选</span>
          <FilterOutline className="text-gray-400" />
        </div>
      </div>
      
      {/* 订单列表 */}
      <div className="bg-white mt-2">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
        >
          <Tabs.Tab title="全部" key="all" />
          <Tabs.Tab title="待审核" key="pending" />
          <Tabs.Tab title="处理中" key="processing" />
          <Tabs.Tab title="已完成" key="completed" />
          <Tabs.Tab title="已取消" key="canceled" />
        </Tabs>
        
        <List className="my-list">
          {getFilteredOrders().map((order) => (
            <List.Item
              key={order.id}
              onClick={() => handleOrderClick(order)}
              arrow={<RightOutline />}
              extra={
                <div className="flex flex-col items-end">
                  <span className="text-red-500 font-medium text-sm">
                    {formatAmount(order.amount)}
                  </span>
                  <span className="text-gray-400 text-xs mt-1">
                    {order.createTime.split(' ')[0]}
                  </span>
                </div>
              }
            >
              <div className="flex flex-col">
                <div className="font-medium text-sm">{order.title}</div>
                <div className="text-xs text-gray-500 mt-1">{order.customer}</div>
                <div className="flex mt-2 flex-wrap gap-1">
                  <Tag
                    style={{
                      color: getStatusStyle(order.status).color,
                      background: getStatusStyle(order.status).bg,
                      border: 'none',
                      padding: '0 4px',
                      fontSize: '10px',
                    }}
                  >
                    {order.status}
                  </Tag>
                  <Tag
                    style={{
                      color: getPayStatusStyle(order.payStatus).color,
                      background: getPayStatusStyle(order.payStatus).bg,
                      border: 'none',
                      padding: '0 4px',
                      fontSize: '10px',
                    }}
                  >
                    {order.payStatus}
                  </Tag>
                  <Tag
                    style={{
                      color: getPriorityStyle(order.priority).color,
                      background: getPriorityStyle(order.priority).bg,
                      border: 'none',
                      padding: '0 4px',
                      fontSize: '10px',
                    }}
                  >
                    优先级: {order.priority}
                  </Tag>
                  <Tag
                    style={{
                      color: '#6b7280',
                      background: '#f3f4f6',
                      border: 'none',
                      padding: '0 4px',
                      fontSize: '10px',
                    }}
                  >
                    {order.type}
                  </Tag>
                </div>
              </div>
            </List.Item>
          ))}
        </List>
      </div>
    </div>
  );
} 