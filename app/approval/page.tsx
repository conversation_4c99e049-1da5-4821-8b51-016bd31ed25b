'use client';

import React, { useState } from 'react';
import { Tabs, List, Tag, Empty, Badge, Space, Dialog, Button } from 'antd-mobile';
import { 
  ClockCircleOutline, 
  CheckCircleOutline, 
  CloseCircleOutline, 
  RightOutline,
  ExclamationCircleOutline,
  AddOutline
} from 'antd-mobile-icons';

// 模拟审批数据
const mockApprovalList = [
  {
    id: 'SP20231001001',
    title: '报销申请',
    applicant: '张三',
    department: '财务部',
    amount: 1280.50,
    reason: '客户招待费用报销',
    status: '待审批',
    submitTime: '2023-10-01 09:30',
    urgency: '普通',
    type: '费用报销',
    nextApprover: '您'
  },
  {
    id: 'SP20231002002',
    title: '请假申请',
    applicant: '李四',
    department: '技术部',
    days: 3,
    reason: '个人事务请假',
    status: '待审批',
    submitTime: '2023-10-02 11:20',
    urgency: '加急',
    type: '请假申请',
    nextApprover: '您'
  },
  {
    id: 'SP20231003003',
    title: '采购申请',
    applicant: '王五',
    department: '行政部',
    amount: 5600.00,
    reason: '办公用品采购',
    status: '已审批',
    submitTime: '2023-10-03 13:45',
    approveTime: '2023-10-03 15:30',
    result: '通过',
    type: '采购申请',
    nextApprover: '刘经理'
  },
  {
    id: 'SP20231004004',
    title: '出差申请',
    applicant: '赵六',
    department: '销售部',
    days: 5,
    destination: '上海',
    reason: '客户拜访',
    status: '已审批',
    submitTime: '2023-10-04 10:15',
    approveTime: '2023-10-04 16:20',
    result: '通过',
    type: '出差申请',
    nextApprover: '陈总监'
  },
  {
    id: 'SP20231005005',
    title: '加班申请',
    applicant: '钱七',
    department: '研发部',
    hours: 4,
    reason: '项目紧急上线',
    status: '已驳回',
    submitTime: '2023-10-05 17:30',
    approveTime: '2023-10-05 18:45',
    result: '驳回',
    rejectReason: '未提前安排工作，请合理规划时间',
    type: '加班申请',
    nextApprover: '孙经理'
  },
  {
    id: 'SP20231006006',
    title: '合同审批',
    applicant: '周八',
    department: '法务部',
    amount: 120000.00,
    reason: '年度服务合同续签',
    status: '审批中',
    submitTime: '2023-10-06 09:00',
    type: '合同审批',
    nextApprover: '吴总'
  },
  {
    id: 'SP20231007007',
    title: '物资领用',
    applicant: '郑九',
    department: '生产部',
    reason: '生产线物料补充',
    status: '待审批',
    submitTime: '2023-10-07 08:30',
    urgency: '紧急',
    type: '物资领用',
    nextApprover: '您'
  },
  {
    id: 'SP20231008008',
    title: '招聘需求',
    applicant: '冯十',
    department: '人力资源部',
    reason: '前端开发工程师2名',
    status: '审批中',
    submitTime: '2023-10-08 14:00',
    type: '招聘申请',
    nextApprover: '杨总'
  }
];

// 获取状态对应的样式和图标
const getStatusStyle = (status: string) => {
  switch (status) {
    case '待审批':
      return { 
        color: '#f59e0b', 
        bg: '#fef3c7',
        icon: <ClockCircleOutline style={{ color: '#f59e0b' }} />
      };
    case '审批中':
      return { 
        color: '#3b82f6', 
        bg: '#dbeafe',
        icon: <ExclamationCircleOutline style={{ color: '#3b82f6' }} />
      };
    case '已审批':
      return { 
        color: '#10b981', 
        bg: '#d1fae5',
        icon: <CheckCircleOutline style={{ color: '#10b981' }} />
      };
    case '已驳回':
      return { 
        color: '#ef4444', 
        bg: '#fee2e2',
        icon: <CloseCircleOutline style={{ color: '#ef4444' }} />
      };
    default:
      return { 
        color: '#6b7280', 
        bg: '#f3f4f6',
        icon: <ClockCircleOutline style={{ color: '#6b7280' }} />
      };
  }
};

// 获取紧急程度对应的样式
const getUrgencyStyle = (urgency: string) => {
  switch (urgency) {
    case '紧急':
      return { color: '#ef4444', bg: '#fee2e2' };
    case '加急':
      return { color: '#f59e0b', bg: '#fef3c7' };
    case '普通':
      return { color: '#10b981', bg: '#d1fae5' };
    default:
      return { color: '#6b7280', bg: '#f3f4f6' };
  }
};

export default function ApprovalPage() {
  const [activeTab, setActiveTab] = useState('todo');
  
  // 根据标签筛选审批
  const getFilteredApprovals = () => {
    switch (activeTab) {
      case 'todo':
        return mockApprovalList.filter(approval => approval.status === '待审批' && approval.nextApprover === '您');
      case 'doing':
        return mockApprovalList.filter(approval => approval.status === '审批中');
      case 'done':
        return mockApprovalList.filter(approval => approval.status === '已审批' || approval.status === '已驳回');
      case 'all':
      default:
        return mockApprovalList;
    }
  };

  // 处理审批详情查看
  const handleApprovalView = (approval: any) => {
    Dialog.alert({
      title: '审批详情',
      content: (
        <div className="p-2">
          <div className="py-1">审批编号: <span className="font-medium">{approval.id}</span></div>
          <div className="py-1">审批标题: <span className="font-medium">{approval.title}</span></div>
          <div className="py-1">申请人: <span className="font-medium">{approval.applicant}</span></div>
          <div className="py-1">所属部门: <span className="font-medium">{approval.department}</span></div>
          {approval.amount && (
            <div className="py-1">申请金额: <span className="font-medium text-red-500">￥{approval.amount.toFixed(2)}</span></div>
          )}
          {approval.days && (
            <div className="py-1">申请天数: <span className="font-medium">{approval.days}天</span></div>
          )}
          {approval.hours && (
            <div className="py-1">申请时长: <span className="font-medium">{approval.hours}小时</span></div>
          )}
          {approval.destination && (
            <div className="py-1">目的地: <span className="font-medium">{approval.destination}</span></div>
          )}
          <div className="py-1">申请原因: <span className="font-medium">{approval.reason}</span></div>
          <div className="py-1">提交时间: <span className="font-medium">{approval.submitTime}</span></div>
          <div className="py-1">审批状态: <span className="font-medium">{approval.status}</span></div>
          {approval.approveTime && (
            <div className="py-1">审批时间: <span className="font-medium">{approval.approveTime}</span></div>
          )}
          {approval.result && (
            <div className="py-1">审批结果: <span className="font-medium">{approval.result}</span></div>
          )}
          {approval.rejectReason && (
            <div className="py-1">驳回原因: <span className="font-medium">{approval.rejectReason}</span></div>
          )}
          <div className="py-1">审批类型: <span className="font-medium">{approval.type}</span></div>
          <div className="py-1">下一审批人: <span className="font-medium">{approval.nextApprover}</span></div>
        </div>
      ),
      closeOnMaskClick: true,
    });
  };

  // 处理审批操作
  const handleApprove = (approval: any) => {
    Dialog.alert({
      title: '审批通过',
      content: `您已通过审批【${approval.title}】`,
      closeOnMaskClick: true,
    });
  };

  // 处理驳回操作
  const handleReject = (approval: any) => {
    Dialog.alert({
      title: '审批驳回',
      content: `您已驳回审批【${approval.title}】`,
      closeOnMaskClick: true,
    });
  };

  // 显示操作菜单
  const showActionMenu = (approval: any) => {
    Dialog.show({
      closeOnAction: true,
      actions: [
        {
          key: 'approve',
          text: '通过审批',
          bold: true,
          onClick: () => handleApprove(approval),
        },
        {
          key: 'reject',
          text: '驳回审批',
          danger: true,
          onClick: () => handleReject(approval),
        },
        {
          key: 'cancel',
          text: '取消',
        },
      ],
    });
  };

  return (
    <div className="pb-16 bg-gray-50">
      {/* 页面标题 */}
      <div className="p-3 text-lg text-center bg-white">审批流</div>
      
      {/* 审批列表 */}
      <div className="bg-white">
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
        >
          <Tabs.Tab title={
            <Badge content={mockApprovalList.filter(a => a.status === '待审批' && a.nextApprover === '您').length || null}>
              待我审批
            </Badge>
          } key="todo" />
          <Tabs.Tab title="进行中" key="doing" />
          <Tabs.Tab title="已完成" key="done" />
          <Tabs.Tab title="全部" key="all" />
        </Tabs>
        
        {getFilteredApprovals().length > 0 ? (
          <List className="my-list">
            {getFilteredApprovals().map((approval) => (
              <List.Item
                key={approval.id}
                onClick={() => handleApprovalView(approval)}
                prefix={getStatusStyle(approval.status).icon}
                arrow={approval.status !== '待审批' || approval.nextApprover !== '您' ? <RightOutline /> : null}
                extra={
                  <div className="flex flex-col items-end">
                    <span className="text-gray-500 text-xs">
                      {approval.submitTime.split(' ')[0]}
                    </span>
                    {approval.urgency && (
                      <Tag
                        style={{
                          color: getUrgencyStyle(approval.urgency).color,
                          background: getUrgencyStyle(approval.urgency).bg,
                          border: 'none',
                          padding: '0 4px',
                          fontSize: '10px',
                          marginTop: '4px'
                        }}
                      >
                        {approval.urgency}
                      </Tag>
                    )}
                    {approval.status === '待审批' && approval.nextApprover === '您' && (
                      <Button
                        style={{
                          fontSize: '12px',
                          padding: '0 8px',
                          height: '22px',
                          marginTop: '8px',
                          minWidth: '40px',
                          lineHeight: '20px',
                          borderRadius: '4px',
                        }}
                        color='primary'
                        onClick={(e) => {
                          e.stopPropagation();
                          showActionMenu(approval);
                        }}
                      >
                        操作
                      </Button>
                    )}
                  </div>
                }
              >
                <div className="flex flex-col">
                  <div className="font-medium text-sm">{approval.title}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {approval.applicant} · {approval.department}
                  </div>
                  <Space className="mt-1">
                    <Tag
                      style={{
                        color: getStatusStyle(approval.status).color,
                        background: getStatusStyle(approval.status).bg,
                        border: 'none',
                        padding: '0 4px',
                        fontSize: '10px',
                      }}
                    >
                      {approval.status}
                    </Tag>
                    <Tag
                      style={{
                        color: '#6b7280',
                        background: '#f3f4f6',
                        border: 'none',
                        padding: '0 4px',
                        fontSize: '10px',
                      }}
                    >
                      {approval.type}
                    </Tag>
                  </Space>
                </div>
              </List.Item>
            ))}
          </List>
        ) : (
          <Empty
            style={{ padding: '32px 0' }}
            imageStyle={{ width: 128 }}
            description={
              activeTab === 'todo' 
                ? '暂无需要您审批的内容' 
                : activeTab === 'doing' 
                  ? '暂无进行中的审批' 
                  : activeTab === 'done' 
                    ? '暂无已完成的审批'
                    : '暂无审批内容'
            }
          />
        )}
      </div>
      
      {/* 底部悬浮按钮 - 发起审批 */}
      <div className="fixed right-4 bottom-20">
        <Button
          color='primary'
          shape='rounded'
          size='large'
          style={{
            width: '48px',
            height: '48px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 0,
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          }}
        >
          <AddOutline fontSize={18} />
        </Button>
      </div>
    </div>
  );
} 