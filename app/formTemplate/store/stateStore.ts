import { create } from 'zustand';
import type { IConfigsStateStore } from '../types/IStateStore';


type IInitialConfigsState = Omit<IConfigsStateStore, '_setConfig' | '_getConfig'>;

export const configsStore = (initialState: IInitialConfigsState) => create<IConfigsStateStore>(
  (set, get) => ({
    ...initialState,
    _setConfig: (configsMap: Record<string, any>) => {
      const { setConfig: setCurrentConfig, getConfig: getCurrentConfig, ...rest } = get();
      set({ ...rest, ...configsMap, setConfig: setCurrentConfig, getConfig: getCurrentConfig });
    },
    _getConfig: (key: string) => {
      const { setConfig: setCurrentConfig, getConfig: getCurrentConfig, ...rest } = get();
      return rest[key];
    }
  })
);

