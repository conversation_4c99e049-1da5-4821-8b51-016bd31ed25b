import { GET } from '../../api/form/layout/route';
import { NextRequest } from 'next/server';
import FormError from './FormError';
import FormClient from './FormClient';
// 服务端组件
// 获取表单布局数据
// 预处理表单配置和数据
const FormServer = async (props: any) => {

  try {
    // 创建模拟的请求对象
    const mockRequest = new NextRequest('http://localhost:3000/api/form/layout');
    
    // 直接调用API路由处理函数获取数据
    const response = await GET(mockRequest);
    const data = await response.json();
    
    // 当获取数据失败时的错误处理
    if (data.code !== 0) {
      return (
        <FormError error={new Error(data.message || '获取表单布局失败')} />
      );
    }
    
    // 获取布局数据并传递给客户端组件
    const layoutData = data.data;

    // 将layout转成map，以baseConfig.formCode加code组成唯一键
    // const layoutMapping = createLayoutMapping(layoutData.layout.layout);
    
    // 渲染包含客户端组件的页面
    return (
      <div>
        <FormClient layoutData={layoutData} />
      </div>
    );
  } catch (error) {
    console.error('表单数据请求错误:', error);
    return (
      <FormError error={new Error(error instanceof Error ? error.message : '未知错误')} />
    );
  }
};

export default FormServer;