'use client';

import { Form } from 'antd-mobile';
import { useEffect, useMemo, useContext, createContext } from 'react';
import { GlobalStore } from '../store/globalStore';
import { configsStore } from '../store/stateStore';
import dynamic from 'next/dynamic';

const FormClient = (props: any) => {
  console.log("🚀 ~ FormClient ~ props:", props);
  const { layoutData, layoutMapping = {} } = props;
  const [form] = Form.useForm();
  const _configsStore = useMemo(() => configsStore(layoutMapping), []);
  const _setConfig = _configsStore(_state => _state._setConfig);
  const _getConfig = _configsStore(_state => _state._getConfig);

  const Test = dynamic(() => import('./FormItem/Test'), {
    loading: () => <div className="loading-placeholder">加载中...</div>,
    ssr: false
  });

  return (
    <GlobalStore.Provider value={{ }}>
      FormClient
      <Test />
    </GlobalStore.Provider>
  )
}

export default FormClient;