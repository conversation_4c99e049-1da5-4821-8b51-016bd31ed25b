'use client';

import React from 'react';
import { Tabs, ProgressBar, Avatar } from 'antd-mobile';
import {
  UnorderedListOutline,
  SendOutline,
  TeamOutline,
  ShopbagOutline,
  ClockCircleOutline,
  CheckCircleOutline,
  CloseCircleOutline,
  ExclamationCircleOutline,
  RightOutline
} from 'antd-mobile-icons';
import Image from 'next/image';
import './page.css';
// 模拟数据
const mockUserData = {
  name: '张三',
  department: '技术部 - 前端开发',
  avatar: '', // 留空使用默认头像
  stats: [
    { label: '待办', value: 28 },
    { label: '已办', value: 12 },
    { label: '发起', value: 6 },
  ]
};

const mockWorkStats = {
  pending: 12,
  completed: 128,
  rejected: 3,
  processing: 25,
  completionRate: 72, // 百分比
};

const mockTodoList = [
  { 
    id: 1, 
    type: '订单审批', 
    title: '销售订单审批表XXX', 
    deadline: '2023-10-25', 
    status: '待处理',
    tagColor: 'bg-orange-500'
  },
  { 
    id: 2, 
    type: '计划审核', 
    title: '产品发布计划V2.1', 
    deadline: '2023-10-24', 
    status: '处理中',
    tagColor: 'bg-blue-500'
  },
  { 
    id: 3, 
    type: '客户服务', 
    title: '客户投诉处理单XYZ', 
    deadline: '2023-10-24', 
    status: '处理中',
    tagColor: 'bg-red-500'
  },
  { 
    id: 4, 
    type: '财务审核', 
    title: '季度财务报表审核', 
    deadline: '2023-10-30', 
    status: '已完成',
    tagColor: 'bg-green-500'
  },
];

const mockAnnouncements = [
  {
    id: 1,
    title: '年度绩效考核启动',
    date: '2023-10-20',
    content: '2023年度绩效考核工作已启动，请各部门负责人于本月底前完成员工考核评价，并提交至人力资源部。'
  },
  {
    id: 2,
    title: '系统升级通知',
    date: '2023-10-23',
    content: '系统将于本周六晚22:00-次日凌晨2:00进行版本升级，期间系统将不可用，请提前做好准备。'
  }
];

export default function WorkspacePage() {
  // 处理图标点击
  const handleIconClick = (type: string) => {
    console.log('点击了功能图标:', type);
    // 实际项目中可以添加跳转逻辑
  };

  // 处理列表项点击
  const handleItemClick = (item: any) => {
    console.log('点击了列表项:', item);
    // 实际项目中可以添加详情页跳转逻辑
  };

  return (
    <div className="pb-16 bg-gray-50 lcp-workspace-page">
      {/* 页面标题 */}
      <div className="p-3 text-lg text-center bg-white">工作台</div>
      
      {/* 用户信息卡片 */}
      <div className="mx-3 mt-3 rounded-lg overflow-hidden bg-blue-600 text-white">
        <div className="p-4 flex items-center">
          <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden mr-3">
            <div className="w-full h-full flex items-center justify-center bg-blue-500 text-white text-xl">
              {mockUserData.name.charAt(0)}
            </div>
          </div>
          <div>
            <div className="text-lg">{mockUserData.name}</div>
            <div className="text-sm opacity-80">{mockUserData.department}</div>
          </div>
        </div>
        <div className="flex justify-center gap-20 p-2">
          {mockUserData.stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-base">{stat.value}</div>
              <div className="text-xs">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
      
      {/* 功能导航区 */}
      <div className="grid grid-cols-4 bg-white mx-4 mt-4 p-4 rounded-lg">
        <div 
          className="flex flex-col items-center" 
          onClick={() => handleIconClick('列表页')}
        >
          <div className="w-12 h-12 bg-blue-100 flex items-center justify-center rounded-lg mb-1 active:bg-blue-200 transition-colors">
            <UnorderedListOutline fontSize={24} color="#3b82f6" />
          </div>
          <div className="text-sm">列表页</div>
        </div>
        <div 
          className="flex flex-col items-center"
          onClick={() => handleIconClick('表单页')}
        >
          <div className="w-12 h-12 bg-green-100 flex items-center justify-center rounded-lg mb-1 active:bg-green-200 transition-colors">
            <SendOutline fontSize={24} color="#22c55e" />
          </div>
          <div className="text-sm">表单页</div>
        </div>
        <div 
          className="flex flex-col items-center"
          onClick={() => handleIconClick('审批流')}
        >
          <div className="w-12 h-12 bg-orange-100 flex items-center justify-center rounded-lg mb-1 active:bg-orange-200 transition-colors">
            <TeamOutline fontSize={24} color="#f97316" />
          </div>
          <div className="text-sm">审批流</div>
        </div>
        <div 
          className="flex flex-col items-center"
          onClick={() => handleIconClick('销售管理')}
        >
          <div className="w-12 h-12 bg-purple-100 flex items-center justify-center rounded-lg mb-1 active:bg-purple-200 transition-colors">
            <ShopbagOutline fontSize={24} color="#8b5cf6" />
          </div>
          <div className="text-sm">销售管理</div>
        </div>
      </div>
      
      {/* 待办事项 */}
      <div className="mx-3 mt-3 bg-white rounded-lg p-4">
        <div className="flex justify-between items-center mb-3">
          <div>待办事项</div>
          <div className="text-blue-500 text-sm flex items-center cursor-pointer active:text-blue-600">
            查看更多 <RightOutline className="ml-1" />
          </div>
        </div>
        
        <Tabs className="lcp-workspace-tabs">
          <Tabs.Tab title="全部" key="all">
            <div className="pt-2">
              {mockTodoList.map(item => (
                <div 
                  key={item.id} 
                  className="flex border-b border-gray-200 py-3 last:border-b-0 active:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleItemClick(item)}
                >
                  <div className="bg-blue-100 text-blue-600 text-xs rounded px-2 py-0.5 h-fit mr-3 whitespace-nowrap">
                    {item.type}
                  </div>
                  <div className="flex-1">
                    <div className="font-normal">{item.title}</div>
                    <div className="text-gray-400 text-xs mt-1">截止: {item.deadline}</div>
                  </div>
                  <div className={`self-center text-xs rounded px-2 py-1 ${
                    item.status === '待处理' ? 'bg-orange-100 text-orange-500' :
                    item.status === '处理中' ? 'bg-blue-100 text-blue-500' :
                    item.status === '已完成' ? 'bg-green-100 text-green-500' : ''
                  }`}>
                    {item.status}
                  </div>
                </div>
              ))}
            </div>
          </Tabs.Tab>
          <Tabs.Tab title="待处理" key="pending">
            <div className="pt-2">
              {mockTodoList.filter(item => item.status === '待处理').map(item => (
                <div 
                  key={item.id} 
                  className="flex border-b border-gray-200 py-3 last:border-b-0 active:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleItemClick(item)}
                >
                  <div className="bg-blue-100 text-blue-600 text-xs rounded px-2 py-0.5 h-fit mr-3 whitespace-nowrap">
                    {item.type}
                  </div>
                  <div className="flex-1">
                    <div className="font-normal">{item.title}</div>
                    <div className="text-gray-400 text-xs mt-1">截止: {item.deadline}</div>
                  </div>
                  <div className="self-center text-xs rounded px-2 py-1 bg-orange-100 text-orange-500">
                    {item.status}
                  </div>
                </div>
              ))}
            </div>
          </Tabs.Tab>
          <Tabs.Tab title="处理中" key="processing">
            <div className="pt-2">
              {mockTodoList.filter(item => item.status === '处理中').map(item => (
                <div 
                  key={item.id} 
                  className="flex border-b border-gray-200 py-3 last:border-b-0 active:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleItemClick(item)}
                >
                  <div className="bg-blue-100 text-blue-600 text-xs rounded px-2 py-0.5 h-fit mr-3 whitespace-nowrap">
                    {item.type}
                  </div>
                  <div className="flex-1">
                    <div className="font-normal">{item.title}</div>
                    <div className="text-gray-400 text-xs mt-1">截止: {item.deadline}</div>
                  </div>
                  <div className="self-center text-xs rounded px-2 py-1 bg-blue-100 text-blue-500">
                    {item.status}
                  </div>
                </div>
              ))}
            </div>
          </Tabs.Tab>
        </Tabs>
      </div>
      
      {/* 工作统计 */}
      <div className="mx-3 mt-3 bg-white rounded-lg p-4">
        <div className="mb-4">工作统计</div>
        
        <div className="grid grid-cols-4 gap-2 mb-6">
          <div className="flex flex-col items-center">
            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-1">
              <ClockCircleOutline fontSize={20} color="#3b82f6" />
            </div>
            <div className="text-xs text-gray-500">待处理</div>
            <div className="text-blue-500">{mockWorkStats.pending}项</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-1">
              <CheckCircleOutline fontSize={20} color="#22c55e" />
            </div>
            <div className="text-xs text-gray-500">已完成</div>
            <div className="text-green-500">{mockWorkStats.completed}项</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mb-1">
              <CloseCircleOutline fontSize={20} color="#ef4444" />
            </div>
            <div className="text-xs text-gray-500">已驳回</div>
            <div className="text-red-500">{mockWorkStats.rejected}项</div>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mb-1">
              <ExclamationCircleOutline fontSize={20} color="#f59e0b" />
            </div>
            <div className="text-xs text-gray-500">处理中</div>
            <div className="text-yellow-500">{mockWorkStats.processing}项</div>
          </div>
        </div>
        
        <div className="mb-4">
          <div className="flex justify-between mb-2">
            <div className="text-sm">本月任务完成率</div>
            <div className="text-blue-500">{mockWorkStats.completionRate}%</div>
          </div>
          <ProgressBar 
            percent={mockWorkStats.completionRate} 
            style={{ 
              '--fill-color': '#3b82f6',
              '--track-color': '#e5e7eb',
              '--track-width': '8px',
            }}
          />
        </div>
      </div>
      
      {/* 通知公告 */}
      <div className="mx-3 mt-3 bg-white rounded-lg p-4 mb-6">
        <div className="flex justify-between items-center mb-3">
          <div>通知公告</div>
          <div className="text-blue-500 text-sm flex items-center cursor-pointer active:text-blue-600">
            查看更多 <RightOutline className="ml-1" />
          </div>
        </div>
        
        {mockAnnouncements.map(item => (
          <div 
            key={item.id} 
            className="border-b border-gray-200 py-3 last:border-b-0 active:bg-gray-50 cursor-pointer transition-colors"
            onClick={() => console.log('点击公告:', item)}
          >
            <div className="font-normal">{item.title}</div>
            <div className="text-gray-400 text-xs mt-1">{item.date}</div>
            <div className="text-sm mt-2 text-gray-600 line-clamp-2">{item.content}</div>
          </div>
        ))}
      </div>
    </div>
  );
} 