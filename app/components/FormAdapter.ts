/**
 * 表单数据处理适配器
 * 用于处理后端返回的布局数据，转换为前端表单组件可用的格式
 */

// 定义字段类型接口
export interface FormFieldType {
  id: string;
  name: string;
  type: string;
  kind: string;
  config?: {
    baseConfig?: {
      name?: string;
      attrType?: string;
      dictId?: string;
      isEnabled?: string;
      isEditable?: string;
    };
    rulesConfig?: Array<{
      isEnabled: string;
      type: string;
      message: string;
      value: any;
    }>;
  };
  children?: FormFieldType[];
}

// 定义布局数据接口
export interface LayoutData {
  layout?: {
    layout?: any[];
    classApiName?: string;
    baseLayoutConfig?: any;
  };
  data?: any;
  layoutAttrMap?: Record<string, any[]>;
  apiConfigMap?: Record<string, any>;
}

/**
 * 递归遍历布局数据，提取所有表单字段
 * @param item 布局项
 * @param fields 提取的字段数组
 */
export function traverseLayout(item: any, fields: FormFieldType[]) {
  if (item.kind === 'FORM_BASE') {
    fields.push(item as FormFieldType);
  }
  if (item.children && Array.isArray(item.children)) {
    item.children.forEach((child: any) => traverseLayout(child, fields));
  }
}

/**
 * 从布局数据中提取所有表单字段
 * @param layoutData 布局数据
 * @returns 表单字段数组
 */
export function extractFormFields(layoutData: LayoutData): FormFieldType[] {
  const formFields: FormFieldType[] = [];
  
  if (layoutData.layout && layoutData.layout.layout) {
    layoutData.layout.layout.forEach(item => traverseLayout(item, formFields));
  }
  
  return formFields;
}

/**
 * 根据字段类型获取默认值
 * @param field 表单字段
 * @returns 默认值
 */
export function getDefaultValue(field: FormFieldType): any {
  const baseConfig = field.config?.baseConfig || {};
  const attrType = baseConfig.attrType || '';
  
  switch (attrType) {
    case 'INT':
      return 0;
    case 'TEXT':
    case 'TEXTAREA':
      return '';
    case 'SELECT':
      return undefined;
    case 'MULTI_SELECT':
      return [];
    case 'RADIO':
      return undefined;
    default:
      return undefined;
  }
}

/**
 * 生成表单初始值对象
 * @param fields 表单字段数组
 * @returns 表单初始值对象
 */
export function generateInitialValues(fields: FormFieldType[]): Record<string, any> {
  const initialValues: Record<string, any> = {};
  
  fields.forEach(field => {
    initialValues[field.id] = getDefaultValue(field);
  });
  
  return initialValues;
}

/**
 * 判断字段是否必填
 * @param field 表单字段
 * @returns 是否必填
 */
export function isFieldRequired(field: FormFieldType): boolean {
  const rulesConfig = field.config?.rulesConfig || [];
  return rulesConfig.some(rule => rule.type === 'required' && rule.isEnabled === '1');
}

/**
 * 判断字段是否可编辑
 * @param field 表单字段
 * @returns 是否可编辑
 */
export function isFieldEditable(field: FormFieldType): boolean {
  const baseConfig = field.config?.baseConfig || {};
  return baseConfig.isEditable !== '0';
}

/**
 * 获取表单标题
 * @param layoutData 布局数据
 * @returns 表单标题
 */
export function getFormTitle(layoutData: LayoutData): string {
  return layoutData.layout?.classApiName || '表单';
}

/**
 * 构建表单验证规则
 * @param field 表单字段
 * @returns 验证规则数组
 */
export function buildFieldRules(field: FormFieldType): any[] {
  const rules: any[] = [];
  const rulesConfig = field.config?.rulesConfig || [];
  const name = field.config?.baseConfig?.name || '字段';
  
  // 处理必填规则
  if (isFieldRequired(field)) {
    rules.push({
      required: true,
      message: `请输入${name}`
    });
  }
  
  // 处理其他规则
  rulesConfig.forEach(rule => {
    if (rule.isEnabled !== '1') return;
    
    switch (rule.type) {
      case 'maxLength':
        rules.push({
          max: rule.value,
          message: rule.message || `${name}最多${rule.value}个字符`
        });
        break;
        
      case 'accuracy':
        // 数字精度规则，通常用于小数位数限制
        if (field.config?.baseConfig?.attrType === 'INT') {
          rules.push({
            validator: (_: any, value: number) => {
              if (value === undefined || value === null) return Promise.resolve();
              
              const decimalPlaces = String(value).split('.')[1]?.length || 0;
              if (decimalPlaces > rule.value) {
                return Promise.reject(new Error(rule.message || `${name}最多${rule.value}位小数`));
              }
              return Promise.resolve();
            }
          });
        }
        break;
    }
  });
  
  return rules;
}

export default {
  extractFormFields,
  generateInitialValues,
  isFieldRequired,
  isFieldEditable,
  getFormTitle,
  buildFieldRules
}; 