import React, { useState } from 'react';
import { Tabs } from 'antd-mobile';
import { FormFieldType } from './FormAdapter';
import FieldRenderer from './FieldRenderer';
import './tabField.css';

interface TabItem {
  title: string;
  key: string;
  children?: FormFieldType[];
}

const TabField = ({ field, store }: { field: FormFieldType, store: any }) => {
  const [activeKey, setActiveKey] = useState('0');
  
  const tabItems: TabItem[] = field.children?.map((child, index) => {
    const tabTitle = child.name || `选项卡 ${index + 1}`;
    return {
      title: tabTitle,
      key: String(index),
      children: child.children || []
    };
  }) || [];

  const handleTabChange = (key: string) => {
    setActiveKey(key);
  };

  return (
    <div>
      <Tabs activeKey={activeKey} onChange={handleTabChange} className='lcp-form-tabs'>
        {tabItems.map((tab) => (
          <Tabs.Tab title={tab.title} key={tab.key}>
            {tab.children && tab.children.map((item, idx) => (
                <FieldRenderer config={item} key={`${tab.key}-${idx}`} store={store} />
              ))}
          </Tabs.Tab>
        ))}
      </Tabs>
    </div>
  );
};

export default TabField; 