import React from 'react';
import { Form, Picker, Space, Tag } from 'antd-mobile';
import FormAdapter, { FormFieldType } from './FormAdapter';

const SelectorField = ({ field }: { field: FormFieldType }) => {
  const baseConfig = field.config?.baseConfig || {};
  const name = baseConfig.name || '未命名字段';
  const attrType = baseConfig.attrType || '';
  const isEditable = FormAdapter.isFieldEditable(field);
  const rules = FormAdapter.buildFieldRules(field);
  const isMultiple = attrType === 'MULTI_SELECT';
  
  // 这里应当根据dictId从后端获取选项数据
  // 暂时使用示例数据
  const options = [
    { label: '选项1', value: '1' },
    { label: '选项2', value: '2' },
    { label: '选项3', value: '3' },
  ];
  
  // 如果字段不可编辑，则显示普通文本
  if (!isEditable) {
    return (
      <Form.Item 
        name={field.id}
        label={name}
      >
        <div className="form-text-display">
          -
        </div>
      </Form.Item>
    );
  }
  
  // 多选模式 - 实际上antd-mobile v5的Picker不支持multiple，需要选择后自行处理
  if (isMultiple) {
    return (
      <Form.Item 
        name={field.id}
        label={name}
        rules={rules}
      >
        <Picker
          columns={[options]}
          key={field.id}
        >
          {(items, { open }) => {
            return (
              <div onClick={open}>
                {items.length > 0 ? (
                  <Space wrap>
                    {items.map((item: any, index: number) => (
                      <Tag key={index}>{item?.label}</Tag>
                    ))}
                  </Space>
                ) : (
                  <span style={{ color: '#cccccc' }}>请选择</span>
                )}
              </div>
            );
          }}
        </Picker>
      </Form.Item>
    );
  }
  
  // 单选模式
  return (
    <Form.Item 
      name={field.id}
      label={name}
      rules={rules}
    >
      <Picker
        columns={[options]}
        key={field.id}
      >
        {(items, { open }) => {
          return (
            <div onClick={open}>
              {items.length > 0 ? (
                items.map((item: any) => item?.label).join('，')
              ) : (
                <span style={{ color: '#cccccc' }}>请选择</span>
              )}
            </div>
          );
        }}
      </Picker>
    </Form.Item>
  );
};

export default SelectorField; 