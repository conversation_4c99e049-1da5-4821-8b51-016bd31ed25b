import React from 'react';
import LayoutRenderer from './LayoutRenderer';
import TextInput from './TextInput';
import Text<PERSON>reaField from './TextAreaField';
import SelectorField from './SelectorField';
import StepperField from './StepperField';
import RadioField from './RadioField';
import Defa<PERSON><PERSON>ield from './DefaultField';
import TabField from './TabField';

const TabDemo = () => {
  // 示例布局数据
  const layoutData = {
    id: 'tab-container',
    name: '标签页容器',
    type: 'CONTAINER',
    kind: 'FORM_CONTAINER',
    config: {
      baseConfig: {
        attrType: 'TAB',
        name: '标签页容器'
      }
    },
    children: [
      // 第一个标签页
      {
        id: 'tab1',
        name: '基本信息',
        type: 'CONTAINER',
        kind: 'FORM_CONTAINER',
        children: [
          {
            id: 'field1',
            name: '姓名',
            type: 'FIELD',
            kind: 'FORM_BASE',
            config: {
              baseConfig: {
                attrType: 'TEXT',
                name: '姓名'
              }
            }
          },
          {
            id: 'field2',
            name: '年龄',
            type: 'FIELD',
            kind: 'FORM_BASE',
            config: {
              baseConfig: {
                attrType: 'INT',
                name: '年龄'
              }
            }
          }
        ]
      },
      // 第二个标签页
      {
        id: 'tab2',
        name: '联系方式',
        type: 'CONTAINER',
        kind: 'FORM_CONTAINER',
        children: [
          {
            id: 'field3',
            name: '电话',
            type: 'FIELD',
            kind: 'FORM_BASE',
            config: {
              baseConfig: {
                attrType: 'TEXT',
                name: '电话'
              }
            }
          },
          {
            id: 'field4',
            name: '地址',
            type: 'FIELD',
            kind: 'FORM_BASE',
            config: {
              baseConfig: {
                attrType: 'TEXTAREA',
                name: '地址'
              }
            }
          }
        ]
      }
    ]
  };

  // 创建store对象，用于解析组件类型
  const store = {
    getComponent: (attrType: string) => {
      const fieldTypeMap = {
        TEXT: TextInput,
        TEXTAREA: TextAreaField,
        SELECT: SelectorField,
        MULTI_SELECT: SelectorField,
        INT: StepperField,
        RADIO: RadioField,
        TAB: TabField,
        DEFAULT: DefaultField
      };
      // 返回实际的组件
      return fieldTypeMap[attrType as keyof typeof fieldTypeMap] || fieldTypeMap.DEFAULT;
    }
  };

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4">标签页组件演示</h1>
      <div className="border rounded p-4">
        <LayoutRenderer item={layoutData} layoutMapping={{}} />
      </div>
    </div>
  );
};

export default TabDemo; 