
import { FormFieldType } from './FormAdapter';
// 字段渲染组件
const FieldRenderer = ({ config, store }: { config: FormFieldType, store: any }) => {
  console.log("🚀 ~ FieldRenderer ~ config:", config)
  const baseConfig = config.config?.baseConfig || {};
  const attrType = baseConfig.attrType || '';
  // const FieldComponent = fieldTypeMap[attrType as keyof typeof fieldTypeMap] || fieldTypeMap.DEFAULT;
  const FieldComponent = store?.getComponent?.(attrType);
  
  return (
    <FieldComponent field={config} store={store} >
      {
        config.children?.map((child: any, index: number) => (
          <FieldRenderer config={child} key={index} store={store} />
        ))
      }
    </FieldComponent>
  );
};

export default FieldRenderer;