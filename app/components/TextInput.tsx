import React, { useContext } from 'react';
import { Form, Input } from 'antd-mobile';
import FormAdapter, { FormFieldType } from './FormAdapter';
import { FormContext } from './FormClient';

// 文本字段类型定义
const TEXT_TYPE = '文本';

const Text = (props: any) => {
  // console.log("🚀 ~ Text ~ props:", props)
  return <Input {...props} />;
};

const TextInput = ({ field }: { field: FormFieldType }) => {
  const baseConfig = field.config?.baseConfig || {};
  const name = baseConfig.name || '未命名字段';
  const isEditable = FormAdapter.isFieldEditable(field);
  const rules = FormAdapter.buildFieldRules(field);
  const store = useContext(FormContext);
  const formCode = baseConfig.formCode || '';
  const code = field.code || '';
  const formCodeAndCode = `${formCode}:${code}`;
  const fieldConfig = store(_state => _state[formCodeAndCode]);
  const required = fieldConfig.config?.rulesConfig?.find((rule: any) => rule.type === 'required')?.value == '1' || false;
  console.log("🚀 ~ TextInput ~ store:", fieldConfig)
  return (
    <Form.Item 
      name={field.code}
      label={name}
      rules={rules}
      required={required}
    >
      <Text 
        placeholder={`请输入${name}`} 
        disabled={!isEditable}
      />
    </Form.Item>
  );
};

export default TextInput; 