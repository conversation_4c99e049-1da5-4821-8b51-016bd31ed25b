'use client';

import { useRouter, usePathname } from 'next/navigation';
import { TabBar as AntTabBar } from 'antd-mobile';
import { 
  AppOutline, 
  FileOutline,
  UserOutline,
  UnorderedListOutline,
  CheckCircleOutline 
} from 'antd-mobile-icons';

const TabBar = () => {
  const router = useRouter();
  const pathname = usePathname();

  const tabs = [
    {
      key: '/workspace',
      title: '工作台',
      icon: <AppOutline />,
    },
    {
      key: '/list',
      title: '列表',
      icon: <UnorderedListOutline />,
    },
    {
      key: '/form',
      title: '表单',
      icon: <FileOutline />,
    },
    {
      key: '/approval',
      title: '审批',
      icon: <CheckCircleOutline />,
    },
    {
      key: '/formTemplate',
      title: '表单模板',
      icon: <UserOutline />,
    },
  ];

  const handleTabChange = (key: string) => {
    router.push(key);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 border-t border-gray-200 bg-white">
      <AntTabBar activeKey={pathname} onChange={handleTabChange}>
        {tabs.map(item => (
          <AntTabBar.Item key={item.key} icon={item.icon} title={item.title} />
        ))}
      </AntTabBar>
    </div>
  );
};

export default TabBar; 