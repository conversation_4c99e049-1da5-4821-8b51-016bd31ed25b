'use client';

import React, { useEffect, useMemo, useContext, createContext } from 'react';
import { Form } from 'antd-mobile';
import { LayoutData } from './FormAdapter';
import LayoutRenderer from './LayoutRenderer';
import { create } from 'zustand';

// 创建FormContext
export const FormContext = createContext<any>(null);

// 表单客户端组件
const FormClient = ({ layoutData, layoutMapping }: { layoutData: LayoutData, layoutMapping: Record<string, any> }) => {
  console.log("🚀 ~ FormClient ~ layoutData:", layoutData, layoutMapping);
  const [form] = Form.useForm();
  
  // 使用zustand store
  const store = useMemo(() => {
    return create((set, get) => ({
      ...layoutMapping,
      setLayoutMapping: (layoutMapping: Record<string, any>) => {
        const currentLayoutMapping = get();
        set({ ...currentLayoutMapping, ...layoutMapping });
      },
      getLayoutMapping: (key: string) => {
        return get()[key];
      }
    }));
  }, []);

  const setLayoutMapping = store(_state => _state.setLayoutMapping);
  const getLayoutMapping = store(_state => _state.getLayoutMapping);
  // 组件挂载时将layoutMapping设置到store中
  useEffect(() => {
    // 设置布局映射
    // setLayoutMapping(layoutMapping);
    
    // // 初始化表单为加载状态
    // setLoading(true);
    
    // 模拟表单数据加载
    setInterval(() => {
      // 设置初始表单数据
      // setFormData({
      //   'TEXT_4E2444398774DB75': '初始值'
      // });
      
      // 更新表单状态
      // form.setFieldsValue({
      //   'TEXT_4E2444398774DB75': '初始值'
      // });
      // 用store，把'BASIC_INFO_GROUP:TEXT_4E2444398774DB75'的rulesConfig的required设置为true
      // const setLayoutMapping = store(_state => _state.setLayoutMapping);
      const inputConfig = getLayoutMapping('BASIC_INFO_GROUP:TEXT_4E2444398774DB75');
      const requiredConfig = inputConfig.config.rulesConfig.find((rule: any) => rule.type === 'required');
      requiredConfig.value = requiredConfig.value == '1' ? '0' : '1';
      setLayoutMapping({
        'BASIC_INFO_GROUP:TEXT_4E2444398774DB75': {
          ...inputConfig,
        }
      });
      
      // 设置加载完成
      // setLoading(false);
    }, 4000);
  }, []);
  
  // 安全获取布局数据
  const layoutContent = layoutData?.layout || {};
  const layout = layoutContent.layout || [];

  // 表单值变化时同步到zustand store
  const handleValuesChange = (changedValues: any) => {
    // 将变更的值更新到store
    Object.entries(changedValues).forEach(([key, value]) => {
      // updateField(key, value);
    });
  };

  useEffect(() => {
    setInterval(() => {
      const random = Math.random();
      // 更新表单值
      form.setFieldsValue({
        'TEXT_4E2444398774DB75': random,
      });
      
      // 同步更新到store
      // updateField('TEXT_4E2444398774DB75', random);
    }, 3000);
  }, []);
  
  return (
      <div>
        <FormContext.Provider value={store}>
          <Form
            form={form}
            // layout="horizontal"
            onValuesChange={handleValuesChange}
          >
            <div className="grid grid-cols-1 gap-6">
              {layout && layout.length > 0 ? (
                <LayoutRenderer item={layoutContent.layout} />
              ) : (
                <div className="text-gray-500">未找到表单布局数据</div>
              )}
            </div>
          </Form>
        </FormContext.Provider>
      </div>
  );
};

export default FormClient; 