import React from 'react';
import { Form, Radio } from 'antd-mobile';
import FormAdapter, { FormFieldType } from './FormAdapter';

const RadioField = ({ field }: { field: FormFieldType }) => {
  const baseConfig = field.config?.baseConfig || {};
  const name = baseConfig.name || '未命名字段';
  const isEditable = FormAdapter.isFieldEditable(field);
  const rules = FormAdapter.buildFieldRules(field);

  return (
    <Form.Item 
      name={field.code}
      label={name}
      rules={rules}
    >
      <Radio.Group disabled={!isEditable}>
        <div className="flex space-x-4">
          <Radio value="1">是</Radio>
          <Radio value="0">否</Radio>
        </div>
      </Radio.Group>
    </Form.Item>
  );
};

export default RadioField; 