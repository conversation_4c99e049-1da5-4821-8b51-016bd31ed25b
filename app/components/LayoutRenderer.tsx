import React, { useContext } from 'react';
import { FormFieldType } from './FormAdapter';
import TextInput from './TextInput';
import Text<PERSON>reaField from './TextAreaField';
import SelectorField from './SelectorField';
import <PERSON>per<PERSON>ield from './StepperField';
import RadioField from './RadioField';
import DefaultField from './DefaultField';
import TabField from './TabField';
import FieldRenderer from './FieldRenderer';
import { FormContext } from './FormClient';

// 字段类型映射到对应的组件
const fieldTypeMap = {
  TEXT: TextInput,
  TEXTAREA: TextAreaField,
  SELECT: SelectorField,
  MULTI_SELECT: SelectorField,
  INT: StepperField,
  RADIO: RadioField,
  TAB: TabField,
  DEFAULT: DefaultField
};

const store = {
  getComponent: (attrType: string) => {
    return fieldTypeMap[attrType as keyof typeof fieldTypeMap] || fieldTypeMap.DEFAULT;
  }
}

// 字段渲染组件
// const FieldRenderer = ({ config }: { config: FormFieldType }) => {
//   console.log("🚀 ~ FieldRenderer ~ config:", config)
//   const baseConfig = config.config?.baseConfig || {};
//   const attrType = baseConfig.attrType || '';
//   const FieldComponent = fieldTypeMap[attrType as keyof typeof fieldTypeMap] || fieldTypeMap.DEFAULT;
  
//   return (
//     <FieldComponent field={config} />
//   );
// };

// 递归渲染布局组件
const LayoutRenderer = ({ item, layoutMapping }: { item: any, layoutMapping?: Record<string, any> }) => {
  // 从store中获取layoutMapping，如果props中没有提供的话
  const storeLayoutMapping = useContext(FormContext);
  console.log("🚀 ~ LayoutRenderer ~ storeLayoutMapping:", storeLayoutMapping)
  
  // 优先使用props中传入的layoutMapping，如果没有则使用store中的
  const finalLayoutMapping = layoutMapping || storeLayoutMapping;
  
  console.log("🚀 ~ LayoutRenderer ~ item:", item);
  
  // 如果是叶子节点（基本字段）
  if (item.id && !item.children ) {
    const formCode = item.config?.baseConfig?.formCode || '';
    const code = item.code || '';
    return <FieldRenderer config={finalLayoutMapping[`${formCode}:${code}`]} key={item.id} store={store} />;
  }
  
  // 如果是容器节点
  if (item.children) {
    const childrenItems = item.children;
    const formCode = item.config?.baseConfig?.formCode || '';
    const code = item.code || '';
    const mapKey = formCode ? `${formCode}:${code}` : code;
    // 默认容器渲染
    return (
      <DefaultField field={item} store={store}>
        {
          childrenItems.map((child: any, index: number) => (
            <FieldRenderer config={child} key={item.id} store={store} />
          ))
        }
      </DefaultField>
      
    );
  }

  if (Array.isArray(item) ) {
    return (
      item.map((child: any, index: number) => {
        // const formCode = child.config?.baseConfig?.formCode || '';
        // const code = child.code || '';
        // const mapKey = formCode ? `${formCode}:${code}` : code;
        return <LayoutRenderer item={child} layoutMapping={finalLayoutMapping} key={child.id} />
      })
    );
  }
  
  // 无法识别的节点类型
  return <div className="text-gray-500 p-2 border rounded">未知布局元素</div>;
};

export default LayoutRenderer; 