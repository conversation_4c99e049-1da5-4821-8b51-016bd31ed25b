import React, { useContext } from 'react';
import { Form, TextArea } from 'antd-mobile';
import FormAdapter, { FormFieldType } from './FormAdapter';
import { FormContext } from './FormClient';
const Text = (props) => {
  console.log("🚀 ~ Text ~ props:", props)
  return (
    <div>
      <TextArea
        rows={3}
        {...props} />
    </div>
  )
}

const TextAreaField = ({ field }: { field: FormFieldType }) => {
  const baseConfig = field.config?.baseConfig || {};
  console.log("🚀 ~ TextAreaField ~ field:", field)
  const name = baseConfig.name || '未命名字段';
  const isEditable = FormAdapter.isFieldEditable(field);
  const rules = FormAdapter.buildFieldRules(field);
  const formCodeAndCode = `${baseConfig.formCode}:${field.code}`;
  const store = useContext(FormContext);
  const fieldConfig = store(_state => _state[formCodeAndCode]);
  const required = fieldConfig.config?.rulesConfig?.find((rule: any) => rule.type === 'required')?.value == '1' || false;

  return (
    <Form.Item 
      name={field.code}
      label={name}
      rules={rules}
      required={required}
    >
      <Text />
    </Form.Item>
  );
};

export default TextAreaField; 