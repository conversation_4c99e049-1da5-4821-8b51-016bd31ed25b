import React from 'react';
import { Form, Stepper } from 'antd-mobile';
import FormAdapter, { FormFieldType } from './FormAdapter';

const StepperField = ({ field }: { field: FormFieldType }) => {
  const baseConfig = field.config?.baseConfig || {};
  const name = baseConfig.name || '未命名字段';
  const isEditable = FormAdapter.isFieldEditable(field);
  const rules = FormAdapter.buildFieldRules(field);

  return (
    <Form.Item 
      name={field.code}
      label={name}
      rules={rules}
    >
      <Stepper 
        disabled={!isEditable}
        min={0}
      />
    </Form.Item>
  );
};

export default StepperField; 