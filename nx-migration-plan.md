# Nx集成迁移计划

## 第一阶段：Nx工作空间初始化（1-2周）

### 1.1 安装和配置Nx
```bash
# 安装Nx CLI
npm install -g nx@latest

# 在现有项目中初始化Nx
npx nx@latest init

# 安装Next.js插件
npm install --save-dev @nx/next @nx/react @nx/js @nx/eslint @nx/jest
```

### 1.2 创建Nx工作空间结构
```
tz-mobile/
├── apps/
│   └── mobile-app/           # 主应用
├── libs/
│   ├── shared/              # 共享库
│   │   ├── models/          # 数据模型
│   │   ├── constants/       # 常量定义
│   │   ├── interfaces/      # 接口定义
│   │   └── types/           # 类型定义
│   ├── core/                # 核心库
│   │   ├── auth/            # 认证核心
│   │   ├── state/           # 状态管理核心
│   │   ├── network/         # 网络请求核心
│   │   └── router/          # 路由核心
│   ├── ui/                  # UI库
│   │   ├── components/      # 基础组件
│   │   ├── templates/       # 模板组件
│   │   └── theme/           # 主题系统
│   ├── features/            # 功能库
│   │   ├── auth/            # 认证功能
│   │   ├── todo/            # 待办功能
│   │   ├── form/            # 表单功能
│   │   └── list/            # 列表功能
│   └── utils/               # 工具库
│       ├── formatters/      # 格式化工具
│       ├── validators/      # 验证工具
│       └── helpers/         # 辅助函数
├── tools/                   # 工具目录
│   ├── generators/          # 代码生成器
│   └── executors/           # 执行器
├── nx.json                  # Nx配置
└── workspace.json           # 工作空间配置
```

### 1.3 配置文件创建

#### nx.json
```json
{
  "$schema": "./node_modules/nx/schemas/nx-schema.json",
  "targetDefaults": {
    "build": {
      "cache": true,
      "dependsOn": ["^build"],
      "inputs": ["production", "^production"]
    },
    "test": {
      "cache": true,
      "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]
    },
    "lint": {
      "cache": true,
      "inputs": ["default", "{workspaceRoot}/.eslintrc.json"]
    }
  },
  "namedInputs": {
    "default": ["{projectRoot}/**/*", "sharedGlobals"],
    "production": [
      "default",
      "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)",
      "!{projectRoot}/tsconfig.spec.json",
      "!{projectRoot}/jest.config.[jt]s",
      "!{projectRoot}/.eslintrc.json"
    ],
    "sharedGlobals": []
  },
  "generators": {
    "@nx/react": {
      "application": {
        "style": "css",
        "linter": "eslint",
        "bundler": "webpack"
      },
      "component": {
        "style": "css"
      },
      "library": {
        "style": "css",
        "linter": "eslint"
      }
    }
  }
}
```

## 第二阶段：代码迁移和重构（2-3周）

### 2.1 创建主应用
```bash
# 生成Next.js应用
nx g @nx/next:app mobile-app --style=css --appDir=true

# 迁移现有代码到新应用结构
```

### 2.2 创建核心库
```bash
# 创建认证核心库
nx g @nx/react:lib core-auth --directory=libs/core/auth --importPath=@tz-mobile/core-auth

# 创建状态管理核心库
nx g @nx/react:lib core-state --directory=libs/core/state --importPath=@tz-mobile/core-state

# 创建网络请求核心库
nx g @nx/react:lib core-network --directory=libs/core/network --importPath=@tz-mobile/core-network

# 创建路由核心库
nx g @nx/react:lib core-router --directory=libs/core/router --importPath=@tz-mobile/core-router
```

### 2.3 创建共享库
```bash
# 创建共享模型库
nx g @nx/js:lib shared-models --directory=libs/shared/models --importPath=@tz-mobile/shared-models

# 创建共享常量库
nx g @nx/js:lib shared-constants --directory=libs/shared/constants --importPath=@tz-mobile/shared-constants

# 创建共享接口库
nx g @nx/js:lib shared-interfaces --directory=libs/shared/interfaces --importPath=@tz-mobile/shared-interfaces

# 创建共享类型库
nx g @nx/js:lib shared-types --directory=libs/shared/types --importPath=@tz-mobile/shared-types
```

### 2.4 创建UI库
```bash
# 创建基础组件库
nx g @nx/react:lib ui-components --directory=libs/ui/components --importPath=@tz-mobile/ui-components

# 创建模板库
nx g @nx/react:lib ui-templates --directory=libs/ui/templates --importPath=@tz-mobile/ui-templates

# 创建主题库
nx g @nx/react:lib ui-theme --directory=libs/ui/theme --importPath=@tz-mobile/ui-theme
```

### 2.5 创建功能库
```bash
# 创建认证功能库
nx g @nx/react:lib feature-auth --directory=libs/features/auth --importPath=@tz-mobile/feature-auth

# 创建待办功能库
nx g @nx/react:lib feature-todo --directory=libs/features/todo --importPath=@tz-mobile/feature-todo

# 创建表单功能库
nx g @nx/react:lib feature-form --directory=libs/features/form --importPath=@tz-mobile/feature-form

# 创建列表功能库
nx g @nx/react:lib feature-list --directory=libs/features/list --importPath=@tz-mobile/feature-list
```

### 2.6 创建工具库
```bash
# 创建格式化工具库
nx g @nx/js:lib utils-formatters --directory=libs/utils/formatters --importPath=@tz-mobile/utils-formatters

# 创建验证工具库
nx g @nx/js:lib utils-validators --directory=libs/utils/validators --importPath=@tz-mobile/utils-validators

# 创建辅助函数库
nx g @nx/js:lib utils-helpers --directory=libs/utils/helpers --importPath=@tz-mobile/utils-helpers
```

## 第三阶段：依赖关系配置（1周）

### 3.1 配置项目标签和约束
在每个库的project.json中添加标签：

```json
{
  "tags": ["scope:shared", "type:util"]
}
```

### 3.2 配置依赖约束
在nx.json中添加：

```json
{
  "namedInputs": {
    "default": ["{projectRoot}/**/*", "sharedGlobals"],
    "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)"]
  },
  "targetDefaults": {
    "build": {
      "dependsOn": ["^build"]
    }
  },
  "generators": {
    "@nx/react": {
      "library": {
        "linter": "eslint"
      }
    }
  }
}
```

### 3.3 设置ESLint规则
```json
{
  "rules": {
    "@nx/enforce-module-boundaries": [
      "error",
      {
        "enforceBuildableLibDependency": true,
        "allow": [],
        "depConstraints": [
          {
            "sourceTag": "scope:shared",
            "onlyDependOnLibsWithTags": ["scope:shared"]
          },
          {
            "sourceTag": "scope:core",
            "onlyDependOnLibsWithTags": ["scope:shared", "scope:core"]
          },
          {
            "sourceTag": "scope:features",
            "onlyDependOnLibsWithTags": ["scope:shared", "scope:core", "scope:ui"]
          },
          {
            "sourceTag": "scope:ui",
            "onlyDependOnLibsWithTags": ["scope:shared", "scope:core"]
          }
        ]
      }
    ]
  }
}
```

## 第四阶段：工程化工具配置（1周）

### 4.1 配置测试
```bash
# 安装Jest
npm install --save-dev @nx/jest jest

# 生成Jest配置
nx g @nx/jest:configuration --project=mobile-app
```

### 4.2 配置Storybook（可选）
```bash
# 安装Storybook
npm install --save-dev @nx/storybook @storybook/react

# 为UI组件库配置Storybook
nx g @nx/storybook:configuration ui-components
```

### 4.3 配置代码生成器
创建自定义生成器用于快速创建组件和功能模块。

## 第五阶段：CI/CD优化（1周）

### 5.1 配置GitHub Actions
```yaml
name: CI
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: nrwl/nx-set-shas@v3
      - run: npm ci
      - run: npx nx affected --target=lint --parallel=3
      - run: npx nx affected --target=test --parallel=3 --ci --code-coverage
      - run: npx nx affected --target=build --parallel=3
```

### 5.2 配置缓存
```json
{
  "tasksRunnerOptions": {
    "default": {
      "runner": "nx/tasks-runners/default",
      "options": {
        "cacheableOperations": ["build", "lint", "test", "e2e"]
      }
    }
  }
}
```

## 迁移检查清单

- [ ] Nx工作空间初始化完成
- [ ] 所有库创建完成
- [ ] 现有代码迁移到对应库
- [ ] 依赖关系配置正确
- [ ] ESLint规则配置
- [ ] 测试配置完成
- [ ] CI/CD流水线配置
- [ ] 文档更新完成

## 风险和注意事项

1. **迁移风险**：现有代码迁移可能导致功能中断
2. **学习成本**：团队需要学习Nx工具链
3. **构建时间**：初期可能构建时间较长，需要优化
4. **依赖管理**：需要仔细管理库之间的依赖关系

## 预期收益

1. **代码复用**：提高代码复用率50%以上
2. **构建效率**：通过缓存和增量构建提高效率30%以上
3. **开发体验**：统一的工具链和开发流程
4. **架构一致性**：强制的依赖约束确保架构不腐化
